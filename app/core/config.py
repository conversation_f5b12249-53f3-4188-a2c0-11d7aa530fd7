"""
应用配置模块

提供应用配置项，支持从环境变量和.env文件加载配置。
"""

from venv import logger
from pydantic import Field, field_validator, ConfigDict, ValidationInfo
from pydantic_settings import BaseSettings, SettingsConfigDict
from typing import Optional, Dict, Any, List
import os

class Settings(BaseSettings):
    """应用配置类"""
    
    # 应用信息
    APP_NAME: str = Field("Quantization", description="应用名称")
    APP_VERSION: str = Field("1.0.0", description="应用版本")
    APP_ENV: str = Field("development", description="运行环境: development, test, production")
    DEBUG: bool = Field(False, description="是否启用调试模式")
    
    # 数据库配置
    DATABASE_URL: str = Field(
        "sqlite:///./quantization.db", 
        description="数据库连接串，例如: sqlite:///./app.db 或 mysql+pymysql://user:pass@localhost/dbname"
    )
    DATABASE_TYPE: str = Field("sqlite", description="数据库类型")
    SQL_ECHO: bool = Field(False, description="是否打印SQL语句")
    
    # API配置
    API_PREFIX: str = Field("/api", description="API路由前缀")
    API_V1_STR: str = Field("/v1", description="API v1路由前缀")
    API_HOST: str = Field("0.0.0.0", description="API主机地址")
    API_PORT: str = Field("8000", description="API端口")
    
    # 跨域配置
    BACKEND_CORS_ORIGINS: List[str] = Field(
        ["*"], description="允许跨域的源列表，例如 ['http://localhost', 'https://example.com']"
    )
    
    # 数据源配置
    DATA_API_TYPE: str = Field("mairui", description="数据源类型: tushare, akshare, mairui")
    MAIRUI_TOKEN: Optional[str] = Field("test-token", description="迈睿数据API授权令牌")
    TUSHARE_TOKEN: Optional[str] = Field("test-token", description="TuShare数据API授权令牌")
    
    # 调度任务配置
    SCHEDULER_ENABLED: bool = Field(True, description="是否启用任务调度")
    DATA_UPDATE_CRON: str = Field("0 18 * * 1-5", description="数据更新cron表达式")
    STOCK_LIST_UPDATE_CRON: str = Field("0 12 * * 0", description="股票列表更新cron表达式")
    
    # 缓存配置
    CACHE_ENABLED: bool = Field(True, description="是否启用缓存")
    CACHE_EXPIRATION: int = Field(3600, description="缓存过期时间(秒)")
    REDIS_URL: Optional[str] = Field(None, description="Redis连接串,例如: redis://localhost:6379/0")
    
    # 日志配置
    LOG_LEVEL: str = Field("INFO", description="日志级别")
    LOG_DIR: str = Field("logs", description="日志文件目录")
    
    @field_validator("DATABASE_URL", mode="before")
    def validate_database_url(cls, v: str, info: ValidationInfo) -> str:
        """验证并规范化数据库连接URL"""
        if v.startswith("sqlite"):
            # 确保SQLite数据库文件目录存在
            db_path = v.replace("sqlite:///", "")
            if db_path != ":memory:":
                os.makedirs(os.path.dirname(os.path.abspath(db_path)), exist_ok=True)
        return v
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=True,
        extra="allow"  # 允许额外字段
    )
    

class SettingsManager:
    """配置管理器,支持重新加载配置"""
    _instance: Optional[Settings] = None
    
    @classmethod
    def get_settings(cls) -> Settings:
        """获取配置实例,如果不存在则创建"""
        if cls._instance is None:
            cls._instance = Settings()
            logger.info(f"读取配置: {cls._instance.model_dump_json()}")
        return cls._instance
    
    @classmethod
    def reload(cls) -> Settings:
        """重新加载配置"""
        cls._instance = Settings()
        logger.info(f"重新加载配置: {cls._instance.model_dump_json()}")
        return cls._instance

# 创建全局设置实例
settings = SettingsManager.get_settings()
